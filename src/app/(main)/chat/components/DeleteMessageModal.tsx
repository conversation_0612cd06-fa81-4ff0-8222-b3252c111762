'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { X, Trash2, AlertTriangle } from 'lucide-react';
import { appTheme } from '@/app/theme';

// Types
interface Message {
  id: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: string;
  type: 'text' | 'image' | 'file' | 'sticker';
  status: 'sending' | 'delivered' | 'read' | 'failed';
  imageUrl?: string;
  user?: {
    id: number;
    firstName: string;
    lastName: string;
    imageUrl?: string;
  };
}

interface DeleteMessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  message: Message | null;
  onConfirmDelete: (messageId: string) => Promise<void>;
}

// Styled components
const ModalOverlay = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: ${props => (props.$isOpen ? 'flex' : 'none')};
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: ${props => props.$isOpen ? 'fadeIn 0.2s ease-out' : 'none'};

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
`;

const ModalContent = styled.div`
  background-color: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  width: 100%;
  max-width: 500px;
  box-shadow: ${appTheme.shadows.lg};
  overflow: hidden;
  animation: ${props => 'slideIn 0.3s ease-out'};

  @keyframes slideIn {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.xl} ${appTheme.spacing['2xl']};
  border-bottom: 1px solid ${appTheme.colors.border};
`;

const ModalTitle = styled.h2`
  margin: 0;
  font-size: ${appTheme.typography.fontSizes.xl};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.error.main};
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${appTheme.colors.text.secondary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.sm};
  
  &:hover {
    background-color: ${appTheme.colors.background.light};
    color: ${appTheme.colors.text.primary};
  }
`;

const ModalBody = styled.div`
  padding: ${appTheme.spacing.xl} ${appTheme.spacing['2xl']};
`;

const WarningContainer = styled.div`
  display: flex;
  align-items: flex-start;
  gap: ${appTheme.spacing.md};
  padding: ${appTheme.spacing.md};
  background-color: #fef3cd;
  border: 1px solid #f59e0b;
  border-radius: ${appTheme.borderRadius.md};
  margin-bottom: ${appTheme.spacing.lg};
`;

const WarningIcon = styled.div`
  color: #f59e0b;
  flex-shrink: 0;
  margin-top: 2px;
`;

const WarningContent = styled.div`
  flex: 1;
`;

const WarningTitle = styled.h3`
  margin: 0 0 ${appTheme.spacing.xs} 0;
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: #92400e;
`;

const WarningText = styled.p`
  margin: 0;
  font-size: ${appTheme.typography.fontSizes.sm};
  color: #92400e;
  line-height: 1.5;
`;

const MessagePreview = styled.div`
  background-color: ${appTheme.colors.background.light};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  padding: ${appTheme.spacing.md};
  margin-bottom: ${appTheme.spacing.lg};
`;

const MessagePreviewLabel = styled.div`
  font-size: ${appTheme.typography.fontSizes.xs};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.secondary};
  margin-bottom: ${appTheme.spacing.xs};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const MessagePreviewContent = styled.div`
  font-size: ${appTheme.typography.fontSizes.sm};
  color: ${appTheme.colors.text.primary};
  line-height: 1.5;
  max-height: 100px;
  overflow-y: auto;
  word-break: break-word;
`;

const MessagePreviewTime = styled.div`
  font-size: ${appTheme.typography.fontSizes.xs};
  color: ${appTheme.colors.text.secondary};
  margin-top: ${appTheme.spacing.xs};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${appTheme.spacing.md};
  justify-content: flex-end;
`;

const Button = styled.button<{ $variant?: 'primary' | 'danger' }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;

  ${props =>
    props.$variant === 'danger'
      ? `
    background-color: ${appTheme.colors.error.main};
    color: white;
    
    &:hover:not(:disabled) {
      background-color: ${appTheme.colors.error};
    }
  `
      : `
    background-color: transparent;
    color: ${appTheme.colors.text.secondary};
    border-color: ${appTheme.colors.border};
    
    &:hover:not(:disabled) {
      background-color: ${appTheme.colors.background};
      color: ${appTheme.colors.text.primary};
    }
  `}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const LoadingSpinner = styled.div`
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

export default function DeleteMessageModal({
  isOpen,
  onClose,
  message,
  onConfirmDelete,
}: DeleteMessageModalProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!message) return;

    setIsDeleting(true);
    try {
      await onConfirmDelete(message.id);
      onClose();
    } catch (error) {
      console.error('Error deleting message:', error);
      // Error handling is done in the parent component
    } finally {
      setIsDeleting(false);
    }
  };

  const formatTime = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString();
    } catch {
      return timestamp;
    }
  };

  if (!isOpen || !message) return null;

  return (
    <ModalOverlay $isOpen={isOpen} onClick={onClose}>
      <ModalContent onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>
            <Trash2 size={20} />
            Delete Message
          </ModalTitle>
          <CloseButton onClick={onClose} aria-label="Close modal">
            <X size={18} />
          </CloseButton>
        </ModalHeader>

        <ModalBody>
          <WarningContainer>
            <WarningIcon>
              <AlertTriangle size={20} />
            </WarningIcon>
            <WarningContent>
              <WarningTitle>Permanent Deletion</WarningTitle>
              <WarningText>
                This action cannot be undone. The message will be permanently removed from the chat
                for all participants.
              </WarningText>
            </WarningContent>
          </WarningContainer>

          <MessagePreview>
            <MessagePreviewLabel>Message to be deleted:</MessagePreviewLabel>
            <MessagePreviewContent>{message.content}</MessagePreviewContent>
            <MessagePreviewTime>Sent: {formatTime(message.timestamp)}</MessagePreviewTime>
          </MessagePreview>

          <ButtonGroup>
            <Button type="button" onClick={onClose} disabled={isDeleting}>
              Cancel
            </Button>
            <Button type="button" $variant="danger" onClick={handleDelete} disabled={isDeleting}>
              {isDeleting ? (
                <>
                  <LoadingSpinner />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 size={16} />
                  Delete Message
                </>
              )}
            </Button>
          </ButtonGroup>
        </ModalBody>
      </ModalContent>
    </ModalOverlay>
  );
}
